# CEP Extension Elements Reference Table

| Main Element | Sub Element | Purpose | Files Involved | Description |
|--------------|-------------|---------|----------------|-------------|
| **Application Root** | - | Main application container | `client/src/App.tsx`, `client/src/main.tsx`, `client/index.html` | Root React component that orchestrates all UI elements with error boundaries |
| **Top Bar** | - | Navigation and status header | `client/src/components/TopBar/TopBar.tsx` | Header containing provider status, model selector, and action buttons |
| | Status Indicator | Provider connection status | `client/src/components/ui/ProviderStatusIndicator.tsx` | Colored dot showing online/offline/checking status of active provider |
| | Model Selector Button | Opens provider configuration | `client/src/components/TopBar/TopBar.tsx` | Clickable button displaying current provider and model selection |
| | New Chat Button | Creates new chat session | `client/src/components/TopBar/TopBar.tsx` | Plus icon button to start new conversation |
| | History Button | Opens chat history modal | `client/src/components/TopBar/TopBar.tsx` | History icon button to view past conversations |
| | Settings Button | Opens settings modal | `client/src/components/TopBar/TopBar.tsx` | Settings icon button to configure application |
| **Chat Area** | - | Main conversation interface | `client/src/components/Chat/ChatMessages.tsx` | Scrollable container for chat messages |
| | Empty State | Welcome message when no chat | `client/src/components/Chat/ChatMessages.tsx` | Centered message with icon when no conversation exists |
| | Chat Messages | Individual message bubbles | `client/src/components/Chat/ChatMessage.tsx` | User and assistant message containers with styling |
| | Code Blocks | Syntax-highlighted code display | `client/src/components/Chat/ShikiCodeBlock.tsx` | Code blocks with copy/download buttons and syntax highlighting |
| | Language Label | Shows programming language | `client/src/components/Chat/ShikiCodeBlock.tsx` | Language name display (py, js, etc.) - **Currently extracted but not displayed** |
| | Copy Button | Copies code to clipboard | `client/src/components/Chat/ShikiCodeBlock.tsx` | Copy icon button within code blocks |
| | Download Button | Saves code to file | `client/src/components/Chat/ShikiCodeBlock.tsx` | Download icon button within code blocks |
| | Collapse/Expand Button | Shows/hides code content | `client/src/components/Chat/ShikiCodeBlock.tsx` | **Missing** - Button to collapse/expand code blocks |
| | Run in Terminal Button | Executes code in terminal | `client/src/components/Chat/ShikiCodeBlock.tsx` | **Missing** - Button to run code (executeCode function exists in cepIntegration.ts) |
| **Input Area** | - | Message composition interface | `client/src/components/Chat/InputArea.tsx` | Bottom input section for typing messages |
| | Attach Button | Opens file system browser | `client/src/components/Chat/InputArea.tsx` | Paperclip icon button to attach files (opens system finder/explorer) |
| | Text Area | Multi-line message input | `client/src/components/Chat/InputArea.tsx` | Auto-resizing textarea with character limit |
| | Voice Input Button | Voice recording functionality | `client/src/components/Chat/InputArea.tsx` | Microphone icon button for voice input |
| | Send Button | Submits message | `client/src/components/Chat/InputArea.tsx` | Send icon button to submit typed message |
| | Character Counter | Shows remaining characters | `client/src/components/Chat/InputArea.tsx` | Visual indicator of message length limits |
| | Keyboard Shortcuts Help | Input usage instructions | `client/src/components/Chat/InputArea.tsx` | Text showing "Enter to send, Shift+Enter for new line" |
| **Modal System** | - | Overlay interface system | `client/src/components/Modals/ModalRoot.tsx` | Central modal router and backdrop |
| **Provider Modal** | - | AI provider configuration | `client/src/components/Modals/ProviderModal.tsx` | Modal for selecting and configuring AI providers |
| | Provider Selector | Dropdown for provider choice | `client/src/components/ui/SearchableProviderSelect.tsx` | Searchable dropdown with provider options |
| | Provider Search Button | Search within provider dropdown | `client/src/components/ui/SearchableProviderSelect.tsx` | Search icon button in provider selector |
| | API Key Input | Credential input for API-based providers | `client/src/components/ui/BaseProviderComponent.tsx` | Secure text input for API keys |
| | Base URL Input | URL input for local providers | `client/src/components/ui/BaseProviderComponent.tsx` | URL input field for local service endpoints |
| | Model Selector | Dropdown for model selection | `client/src/components/ui/SearchableModelSelect.tsx` | Searchable dropdown with available models |
| | Model Search Button | Search within model dropdown | `client/src/components/ui/SearchableModelSelect.tsx` | Search icon button in model selector |
| | Retry Button | Retries failed model loading | `client/src/components/ui/BaseProviderComponent.tsx` | Button to retry model loading after errors |
| | Loading State | Shows model loading progress | `client/src/components/ui/BaseProviderComponent.tsx` | Spinner and loading text during model fetch |
| | Error State | Displays configuration errors | `client/src/components/ui/BaseProviderComponent.tsx` | Error messages with retry options |
| | Save Button | Confirms provider configuration | `client/src/components/ui/BaseProviderComponent.tsx` | Button to save provider settings |
| **Settings Modal** | - | Application configuration | `client/src/components/Modals/SettingsModal.tsx` | Multi-tab settings interface |
| | General Tab | Basic application settings | `client/src/components/Modals/SettingsModal.tsx` | Theme, notifications, and behavior settings |
| | Analytics Tab | Usage statistics display | `client/src/components/Modals/SettingsModal.tsx` | Charts and metrics about extension usage |
| | Help Tab | Support and documentation | `client/src/components/Modals/SettingsModal.tsx` | Help links and troubleshooting information |
| | About Tab | Extension information | `client/src/components/Modals/SettingsModal.tsx` | Version, credits, and legal information |
| **Chat History Modal** | - | Past conversations browser | `client/src/components/Modals/ChatHistoryModal.tsx` | Interface for viewing and managing chat history |
| | Search Bar | Filter conversations | `client/src/components/Modals/ChatHistoryModal.tsx` | Text input to search through chat sessions |
| | Sort Dropdown | Order conversations | `client/src/components/Modals/ChatHistoryModal.tsx` | Dropdown to sort by date, name, or other criteria |
| | Session List | List of past conversations | `client/src/components/Modals/ChatHistoryModal.tsx` | Scrollable list of clickable chat sessions |
| | Session Preview | Shows conversation details | `client/src/components/Modals/ChatHistoryModal.tsx` | Preview pane showing selected conversation |
| | Delete Button | Removes conversations | `client/src/components/Modals/ChatHistoryModal.tsx` | Trash icon to delete selected sessions |
| **Status Modal** | - | Detailed provider status | `client/src/components/Modals/StatusModal.tsx` | Comprehensive provider connection information |
| | Connection Status | Online/offline indicator | `client/src/components/Modals/StatusModal.tsx` | Detailed status with latency and error information |
| | Refresh Button | Manual status check | `client/src/components/Modals/StatusModal.tsx` | Button to manually refresh provider status |
| | Error Details | Detailed error information | `client/src/components/Modals/StatusModal.tsx` | Expanded error messages and troubleshooting |
| **Toast System** | - | Notification system | `client/src/components/ui/Toast.tsx` | Temporary notification messages |
| | Toast Container | Notification display area | `client/src/components/ui/Toast.tsx` | Fixed position container for toast messages |
| | Toast Item | Individual notification | `client/src/components/ui/Toast.tsx` | Single toast with icon, message, and close button |
| | Success Toast | Positive feedback messages | `client/src/components/stores/toastStore.ts` | Green toast for successful operations |
| | Error Toast | Error notification messages | `client/src/components/stores/toastStore.ts` | Red toast for error conditions |
| | Warning Toast | Warning notification messages | `client/src/components/stores/toastStore.ts` | Yellow toast for warning conditions |
| | Info Toast | Informational messages | `client/src/components/stores/toastStore.ts` | Blue toast for general information |
| | Toast Icons | Visual indicators by type | `client/src/components/ui/Toast.tsx` | CheckCircle, AlertCircle, AlertTriangle, Info icons |
| | Toast Close Button | Dismisses individual toasts | `client/src/components/ui/Toast.tsx` | X icon button to close toast messages |
| **Error Boundaries** | - | Error handling system | `client/src/components/ErrorBoundary.tsx` | React error boundaries for graceful failure |
| | Global Error Boundary | Application-level error handling | `client/src/components/ErrorBoundary.tsx` | Catches and displays unhandled React errors |
| | Provider Error Boundary | Provider-specific error handling | `client/src/components/ui/ProviderErrorBoundary.tsx` | Specialized error handling for provider operations |
| **CEP Status Component** | - | CEP environment information | `client/src/components/CEPStatus.tsx` | Component showing current Adobe host application info |
| | Loading State | Shows CEP connection status | `client/src/components/CEPStatus.tsx` | Loading indicator while checking CEP environment |
| | Host Information | Shows Adobe app details | `client/src/components/CEPStatus.tsx` | Displays host application name and version |
| | Browser Mode | Fallback for non-CEP environment | `client/src/components/CEPStatus.tsx` | Shows when running outside Adobe applications |
| **CEP Integration** | - | Adobe CEP communication layer | `client/src/utils/cepIntegration.ts` | Bridge between web UI and Adobe applications |
| | CSInterface | Adobe CEP API wrapper | `client/CSInterface.js`, `client/src/types/cep.d.ts` | Official Adobe CEP JavaScript API |
| | ExtendScript Bridge | Communication with host app | `client/src/utils/cepIntegration.ts` | Executes ExtendScript code in Adobe applications |
| | Settings Storage | Persistent configuration storage | `client/src/utils/cepIntegration.ts` | CEP-based settings persistence system |
| **ExtendScript Host** | - | Adobe application integration | `host/ae-integration.jsxinc` | Server-side ExtendScript functionality |
| | Constants | Shared configuration values | `host/constants.jsx` | File paths, timeouts, and default values |
| | Model Data | Provider and model definitions | `host/modelData.jsx` | Fallback model information for all providers |
| | HTTP Client | Network request functionality | `host/ae-integration.jsxinc` | Socket-based HTTP client for API calls |
| | File System | Settings and history persistence | `host/ae-integration.jsxinc` | File-based storage for user data |
| **State Management** | - | Application state system | Various store files | Zustand-based state management |
| | Settings Store | Provider and configuration state | `client/src/stores/settingsStore.ts` | Manages provider configs, models, and settings |
| | Chat Store | Conversation state | `client/src/components/stores/chatStore.ts` | Manages messages, sessions, and chat state |
| | Modal Store | UI modal state | `client/src/components/stores/modalStore.ts` | Controls which modal is currently open |
| | Toast Store | Notification state | `client/src/components/stores/toastStore.ts` | Manages toast message queue and display |
| | History Store | Chat history state | `client/src/components/stores/historyStore.ts` | Manages past conversation storage and retrieval |
| **Build System** | - | Development and deployment | Build configuration files | Vite-based build system with CEP integration |
| | Vite Configuration | Build tool setup | `vite.config.ts` | Development server and build configuration |
| | CEP File Copier | Asset deployment | `copy-cep-files.js` | Copies CEP-specific files to distribution |
| | Manifest | CEP extension definition | `CSXS/manifest.xml` | Adobe CEP extension configuration |
| | Icons | Extension visual identity | `icons/` directory | Extension icons for Adobe applications |
| | Debug Configuration | Development settings | `.debug` file | Enables CEP debug mode |
